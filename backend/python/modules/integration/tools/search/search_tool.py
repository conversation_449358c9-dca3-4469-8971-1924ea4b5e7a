from typing import List
from enum import Enum
from abc import ABC, abstractmethod

from modules.common.schema import CodeSnippet

class SearchToolABC(ABC):
    """搜索引擎基类"""
    
    def __init__(self, repo_path: str, **args):
        pass
    
    @abstractmethod
    def search(self, query: str, **kwargs) -> List[CodeSnippet]:
        raise NotImplementedError("子类必须实现search方法")

    @property
    @abstractmethod
    def description(self):
        raise NotImplementedError("子类必须实现description属性")

    @property
    @abstractmethod
    def examples(self):
        raise NotImplementedError("子类必须实现examples属性")
    
class SearchToolEnum(Enum):
    GREP = "grep"
    EMBEDDING = "embedding"
    INVERTED_INDEX = "inverted_index"
    TERM_SPRSE = "term_sparse"
    ANY = "any"
    SUB_QUERY = "sub_query"