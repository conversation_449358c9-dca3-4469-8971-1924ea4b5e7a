#!/usr/bin/env python3
"""
测试 SearchToolEnum 修复
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.integration.tools.search.search_tool import SearchToolEnum


def test_enum_value_checking():
    """测试枚举值检查逻辑"""
    print("=== 测试枚举值检查逻辑 ===")
    
    # 模拟 meta_info 数据
    test_cases = [
        {"tool": "grep"},
        {"tool": "GREP"},
        {"tool": "inverted_index"},
        {"tool": "INVERTED_INDEX"},
        {"tool": "term_sparse"},
        {"tool": "TERM_SPARSE"},
        {"tool": "any"},
        {"tool": "ANY"},
        {"tool": "embedding"},
        {"tool": "invalid_tool"},
        {},  # 没有 tool 字段
    ]
    
    for i, meta_info in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}: {meta_info}")
        
        # 模拟修复后的逻辑
        search_tool = SearchToolEnum.TERM_SPRSE  # 默认值
        
        if "tool" in meta_info:
            tool_value = meta_info["tool"].lower()
            try:
                search_tool = SearchToolEnum(tool_value)
                print(f"  ✅ 成功: {tool_value} -> {search_tool}")
            except ValueError:
                print(f"  ⚠️  无效工具类型: {tool_value}，使用默认: {search_tool.value}")
        else:
            print(f"  ℹ️  没有工具字段，使用默认: {search_tool.value}")


def test_original_buggy_logic():
    """测试原始有bug的逻辑"""
    print("\n=== 测试原始有bug的逻辑 ===")
    
    test_tools = ["grep", "inverted_index", "term_sparse", "any"]
    
    print("原始逻辑检查结果:")
    for tool in test_tools:
        # 原始的错误逻辑
        in_member_map = tool.lower() in SearchToolEnum._member_map_
        in_member_names = tool.lower() in SearchToolEnum._member_names_
        
        print(f"  {tool}:")
        print(f"    tool.lower() in _member_map_: {in_member_map}")
        print(f"    tool.lower() in _member_names_: {in_member_names}")
        
        # 这个逻辑永远不会成功
        if in_member_map:
            print(f"    ✅ 原始逻辑会成功")
        else:
            print(f"    ❌ 原始逻辑会失败")


def test_correct_enum_checking_methods():
    """测试正确的枚举检查方法"""
    print("\n=== 测试正确的枚举检查方法 ===")
    
    test_tools = ["grep", "inverted_index", "term_sparse", "any", "invalid"]
    
    for tool in test_tools:
        print(f"\n检查工具: {tool}")
        
        # 方法1: 直接使用 try/except (推荐)
        try:
            enum_val = SearchToolEnum(tool)
            print(f"  方法1 (try/except): ✅ {enum_val}")
        except ValueError:
            print(f"  方法1 (try/except): ❌ 无效值")
        
        # 方法2: 检查所有枚举值
        valid_values = [e.value for e in SearchToolEnum]
        if tool in valid_values:
            enum_val = SearchToolEnum(tool)
            print(f"  方法2 (检查值列表): ✅ {enum_val}")
        else:
            print(f"  方法2 (检查值列表): ❌ 无效值")
        
        # 方法3: 使用 hasattr (不推荐，因为检查的是属性名)
        if hasattr(SearchToolEnum, tool.upper()):
            print(f"  方法3 (hasattr): ✅ 有对应属性")
        else:
            print(f"  方法3 (hasattr): ❌ 无对应属性")


def main():
    """主测试函数"""
    print("开始测试 SearchToolEnum 修复...\n")
    
    test_enum_value_checking()
    test_original_buggy_logic()
    test_correct_enum_checking_methods()
    
    print("\n" + "="*60)
    print("总结:")
    print("1. ❌ 原始代码使用 _member_map_ 检查枚举值是错误的")
    print("2. ✅ 修复后使用 try/except 直接构造枚举是正确的")
    print("3. 💡 _member_map_ 的键是枚举名称(大写)，不是枚举值(小写)")
    print("4. 💡 应该直接使用 SearchToolEnum(value) 来验证和转换")


if __name__ == "__main__":
    main()
